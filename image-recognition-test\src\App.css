/* 全局样式重置 */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* 自定义样式 */
.ant-layout {
  background: #f5f5f5;
}

.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 上传区域样式 */
.ant-upload-drag {
  border: 2px dashed #d9d9d9 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease;
}

.ant-upload-drag:hover {
  border-color: #1890ff !important;
  background-color: #fafafa !important;
}

.ant-upload-drag.ant-upload-drag-hover {
  border-color: #1890ff !important;
  background-color: #f0f8ff !important;
}

/* 标签渲染区域样式 */
.label-canvas {
  border: 2px solid #1890ff;
  border-radius: 4px;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.label-element {
  position: absolute;
  border: 1px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.label-element:hover {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }

  .ant-layout-content {
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
