import React from 'react';
import { Card, Typography, Space, Tag, Divider } from 'antd';
import { QRCodeSVG } from 'qrcode.react';

const { Title, Text, Paragraph } = Typography;

// 元素类型映射
const ELEMENT_TYPE_NAMES = {
  '1': '文本',
  '2': '条形码',
  '3': '画布',
  '4': '线条',
  '5': 'Logo',
  '6': '图片',
  '7': '二维码',
  '8': '圆形',
  '9': '时间',
  '10': '表格',
  '11': '矩形'
};

const LabelRenderer = ({ data, canvasWidth = 800, canvasHeight = 600 }) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Card title="标签预览">
        <Text type="secondary">暂无数据</Text>
      </Card>
    );
  }

  // 查找画布元素
  const canvasElement = data.find(item => item.type === '3');
  const actualCanvasWidth = canvasElement ? parseInt(canvasElement.width) || canvasWidth : canvasWidth;
  const actualCanvasHeight = canvasElement ? parseInt(canvasElement.height) || canvasHeight : canvasHeight;

  // 计算缩放比例
  const maxDisplayWidth = 800;
  const maxDisplayHeight = 600;
  const scaleX = Math.min(maxDisplayWidth / actualCanvasWidth, 1);
  const scaleY = Math.min(maxDisplayHeight / actualCanvasHeight, 1);
  const scale = Math.min(scaleX, scaleY);

  const displayWidth = actualCanvasWidth * scale;
  const displayHeight = actualCanvasHeight * scale;

  // 过滤掉画布元素，只渲染其他元素
  const renderElements = data.filter(item => item.type !== '3');

  const renderElement = (element, index) => {
    const x = (parseInt(element.x) || 0) * scale;
    const y = (parseInt(element.y) || 0) * scale;
    const width = (parseInt(element.width) || 100) * scale;
    const height = (parseInt(element.height) || 20) * scale;
    const rotation = parseInt(element.rotational) || 0;

    const baseStyle = {
      position: 'absolute',
      left: `${x}px`,
      top: `${y}px`,
      width: `${width}px`,
      height: `${height}px`,
      transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
      transformOrigin: 'top left',
      border: '1px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: `${Math.max(10, height * 0.6)}px`,
      overflow: 'hidden'
    };

    switch (element.type) {
      case '1': // 文本
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(24, 144, 255, 0.1)',
              borderColor: '#1890ff',
              padding: '2px',
              textAlign: element.hAlignment === '2' ? 'center' : 
                        element.hAlignment === '3' ? 'right' : 'left',
              fontWeight: element.bold === 'true' ? 'bold' : 'normal',
              fontStyle: element.italic === 'true' ? 'italic' : 'normal',
              textDecoration: element.underline === 'true' ? 'underline' : 'none'
            }}
            title={`文本: ${element.content || '空文本'}`}
          >
            <Text style={{ 
              fontSize: 'inherit',
              lineHeight: '1.2',
              wordBreak: 'break-all'
            }}>
              {element.content || '文本'}
            </Text>
          </div>
        );

      case '2': // 条形码
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              borderColor: '#52c41a',
              flexDirection: 'column'
            }}
            title={`条形码: ${element.content || '123456'}`}
          >
            <div style={{ 
              backgroundColor: '#000',
              width: '80%',
              height: '60%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '8px'
            }}>
              |||||||
            </div>
            {element.showText === 'true' && (
              <Text style={{ fontSize: '8px', marginTop: '2px' }}>
                {element.content || '123456'}
              </Text>
            )}
          </div>
        );

      case '7': // 二维码
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(250, 173, 20, 0.1)',
              borderColor: '#faad14',
              padding: '4px'
            }}
            title={`二维码: ${element.content || 'QR Code'}`}
          >
            <div style={{ 
              width: '100%', 
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {width > 30 && height > 30 ? (
                <QRCodeSVG
                  value={element.content || 'QR Code'}
                  size={Math.min(width - 8, height - 8)}
                  level="M"
                />
              ) : (
                <div style={{
                  width: '80%',
                  height: '80%',
                  backgroundColor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '6px'
                }}>
                  QR
                </div>
              )}
            </div>
          </div>
        );

      case '6': // 图片
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(114, 46, 209, 0.1)',
              borderColor: '#722ed1'
            }}
            title="图片"
          >
            {element.content && element.content.startsWith('data:image') ? (
              <img
                src={element.content}
                alt="识别的图片"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <Text style={{ fontSize: 'inherit' }}>图片</Text>
            )}
          </div>
        );

      case '10': // 表格
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(235, 47, 6, 0.1)',
              borderColor: '#eb2f06',
              padding: '2px'
            }}
            title="表格"
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(20px, 1fr))',
              gridTemplateRows: 'repeat(auto-fit, minmax(10px, 1fr))',
              gap: '1px',
              backgroundColor: '#ccc'
            }}>
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  style={{
                    backgroundColor: 'white',
                    fontSize: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {i < 3 ? 'H' : 'D'}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div
            key={index}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(140, 140, 140, 0.1)',
              borderColor: '#8c8c8c'
            }}
            title={`${ELEMENT_TYPE_NAMES[element.type] || '未知类型'}`}
          >
            <Text style={{ fontSize: 'inherit' }}>
              {ELEMENT_TYPE_NAMES[element.type] || '未知'}
            </Text>
          </div>
        );
    }
  };

  return (
    <Card title="标签预览" style={{ width: '100%' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 画布信息 */}
        <div>
          <Text strong>画布尺寸: </Text>
          <Text>{actualCanvasWidth} × {actualCanvasHeight} px</Text>
          <Text type="secondary" style={{ marginLeft: 16 }}>
            显示比例: {(scale * 100).toFixed(1)}%
          </Text>
        </div>

        {/* 元素统计 */}
        <div>
          <Text strong>元素统计: </Text>
          <Space wrap>
            {Object.entries(
              renderElements.reduce((acc, item) => {
                const typeName = ELEMENT_TYPE_NAMES[item.type] || '未知';
                acc[typeName] = (acc[typeName] || 0) + 1;
                return acc;
              }, {})
            ).map(([type, count]) => (
              <Tag key={type} color="blue">
                {type}: {count}
              </Tag>
            ))}
          </Space>
        </div>

        <Divider />

        {/* 画布渲染区域 */}
        <div
          style={{
            position: 'relative',
            width: `${displayWidth}px`,
            height: `${displayHeight}px`,
            border: '2px solid #1890ff',
            backgroundColor: '#fafafa',
            margin: '0 auto',
            overflow: 'hidden'
          }}
        >
          {renderElements.map((element, index) => renderElement(element, index))}
          
          {renderElements.length === 0 && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center'
              }}
            >
              <Text type="secondary">暂无识别到的元素</Text>
            </div>
          )}
        </div>

        {/* 元素列表 */}
        {renderElements.length > 0 && (
          <Card size="small" title="元素列表">
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {renderElements.map((element, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <Space>
                    <Tag color="blue">
                      {ELEMENT_TYPE_NAMES[element.type] || '未知'}
                    </Tag>
                    <Text>
                      位置: ({element.x}, {element.y})
                    </Text>
                    <Text>
                      尺寸: {element.width} × {element.height}
                    </Text>
                    {element.content && (
                      <Text type="secondary">
                        内容: {element.content.length > 20 
                          ? element.content.substring(0, 20) + '...' 
                          : element.content}
                      </Text>
                    )}
                  </Space>
                </div>
              ))}
            </Space>
          </Card>
        )}
      </Space>
    </Card>
  );
};

export default LabelRenderer;
