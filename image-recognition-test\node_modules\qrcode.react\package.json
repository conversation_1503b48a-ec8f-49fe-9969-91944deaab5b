{"name": "qrcode.react", "version": "4.2.0", "description": "React component to generate QR codes", "keywords": ["react", "react-component", "qrcode"], "homepage": "http://zpao.github.io/qrcode.react", "main": "./lib/index.js", "module": "./lib/esm/index.js", "types": "./lib/index.d.ts", "exports": {".": {"import": {"types": "./lib/index.d.mts", "default": "./lib/esm/index.js"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "scripts": {"build": "pnpm run build:code && pnpm run build:examples", "build:code": "tsup src/index.tsx -d lib --format esm,cjs --dts --legacy-output --target=es2017 --platform=browser", "build:examples": "tsup examples/demo.tsx -d examples --format iife --env.NODE_ENV production --minify --target=es2017 --legacy-output", "build:examples:dev": "tsup examples/demo.tsx -d examples --format iife --env.NODE_ENV development --target=es2017 --legacy-output", "lint": "eslint .", "pretty": "prettier --write '{*,.*}.{mjs,js,json}' '**/*.{js,json}'", "prepack": "make clean && make all && pnpm run typecheck", "prepublish-docs": "make clean && make all", "publish-docs": "gh-pages --dist=examples --src='{index.html,iife/demo.js}'", "test": "jest", "typecheck": "tsc --noEmit"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/zpao/qrcode.react.git"}, "license": "ISC", "files": ["lib"], "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "devDependencies": {"@jest/globals": "^29.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.1.0", "@types/node": "^20.4.10", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "eslint": "^8.6.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^27.1.3", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.5.0", "gh-pages": "^6.0.0", "jest": "^29.2.2", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "ts-jest": "^29.1.0", "tsup": "^8.0.2", "typescript": "^5.0.4"}, "packageManager": "pnpm@9.8.0+sha512.8e4c3550fb500e808dbc30bb0ce4dd1eb614e30b1c55245f211591ec2cdf9c611cabd34e1364b42f564bd54b3945ed0f49d61d1bbf2ec9bd74b866fcdc723276"}