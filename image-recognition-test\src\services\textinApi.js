import axios from 'axios';

/**
 * TextIn API 服务类
 * 用于调用TextIn的通用文档解析API
 */
class TextInApiService {
  constructor() {
    this.baseURL = 'https://api.textin.com/ai/service/v1';
    this.endpoint = '/pdf_to_markdown';
    
    // 默认配置
    this.defaultConfig = {
      char_details: 1,        // 返回字符位置信息
      page_details: 1,        // 返回页面详细信息
      catalog_details: 0,     // 不需要目录信息
      dpi: 144,              // PDF坐标基准
      apply_document_tree: 0, // 不生成标题
      markdown_details: 1,    // 生成markdown详情
      table_flavor: 'html',   // 表格格式
      get_image: 'objects',   // 返回图像对象
      image_output_type: 'base64str', // 图片以base64返回
      parse_mode: 'scan',     // 仅按文字识别模式
      get_excel: 0,          // 不返回excel
      raw_ocr: 1,            // 返回全文识别结果
      paratext_mode: 'annotation', // 非正文内容展示模式
      formula_level: 2,       // 不识别公式
      apply_merge: 1          // 合并段落和表格
    };
  }

  /**
   * 设置API认证信息
   * @param {string} appId - TextIn应用ID
   * @param {string} secretCode - TextIn密钥
   */
  setCredentials(appId, secretCode) {
    this.appId = appId;
    this.secretCode = secretCode;
  }

  /**
   * 获取请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    if (!this.appId || !this.secretCode) {
      throw new Error('请先设置API认证信息');
    }
    
    return {
      'x-ti-app-id': this.appId,
      'x-ti-secret-code': this.secretCode,
      'Content-Type': 'application/octet-stream'
    };
  }

  /**
   * 构建请求URL
   * @param {Object} params - URL参数
   * @returns {string} 完整的请求URL
   */
  buildUrl(params = {}) {
    const config = { ...this.defaultConfig, ...params };
    const url = new URL(this.baseURL + this.endpoint);
    
    Object.keys(config).forEach(key => {
      if (config[key] !== undefined && config[key] !== null) {
        url.searchParams.append(key, config[key]);
      }
    });
    
    return url.toString();
  }

  /**
   * 上传文件并进行识别
   * @param {File|Blob} file - 要识别的文件
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeFile(file, options = {}) {
    try {
      const url = this.buildUrl(options);
      const headers = this.getHeaders();
      
      // 将文件转换为ArrayBuffer
      const fileBuffer = await this.fileToArrayBuffer(file);
      
      console.log('发送TextIn API请求:', {
        url,
        fileSize: fileBuffer.byteLength,
        fileName: file.name
      });

      const response = await axios.post(url, fileBuffer, {
        headers,
        timeout: 60000, // 60秒超时
        maxContentLength: 500 * 1024 * 1024, // 500MB
        maxBodyLength: 500 * 1024 * 1024
      });

      console.log('TextIn API响应:', response.data);
      
      if (response.data.code !== 200) {
        throw new Error(`API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error);
      
      if (error.response) {
        // 服务器响应错误
        const errorData = error.response.data;
        throw new Error(`API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
      } else if (error.request) {
        // 网络错误
        throw new Error('网络错误: 无法连接到TextIn服务器');
      } else {
        // 其他错误
        throw new Error(`请求错误: ${error.message}`);
      }
    }
  }

  /**
   * 通过URL识别文件
   * @param {string} fileUrl - 文件URL
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} API响应结果
   */
  async recognizeUrl(fileUrl, options = {}) {
    try {
      const url = this.buildUrl(options);
      const headers = {
        ...this.getHeaders(),
        'Content-Type': 'text/plain'
      };
      
      console.log('发送TextIn API请求 (URL):', {
        url,
        fileUrl
      });

      const response = await axios.post(url, fileUrl, {
        headers,
        timeout: 60000
      });

      console.log('TextIn API响应:', response.data);
      
      if (response.data.code !== 200) {
        throw new Error(`API错误: ${response.data.message || '未知错误'}`);
      }

      return response.data;
    } catch (error) {
      console.error('TextIn API调用失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 将文件转换为ArrayBuffer
   * @param {File|Blob} file - 文件对象
   * @returns {Promise<ArrayBuffer>} ArrayBuffer
   */
  fileToArrayBuffer(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @returns {Error} 处理后的错误
   */
  handleError(error) {
    if (error.response) {
      const errorData = error.response.data;
      return new Error(`API错误 (${error.response.status}): ${errorData.message || '服务器错误'}`);
    } else if (error.request) {
      return new Error('网络错误: 无法连接到TextIn服务器');
    } else {
      return new Error(`请求错误: ${error.message}`);
    }
  }

  /**
   * 验证文件类型和大小
   * @param {File} file - 文件对象
   * @returns {Object} 验证结果
   */
  validateFile(file) {
    const supportedTypes = [
      'image/png', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/tiff', 'image/webp',
      'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/html', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain'
    ];

    const maxSize = 500 * 1024 * 1024; // 500MB

    if (!supportedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}。支持的类型: ${supportedTypes.join(', ')}`
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > 500MB`
      };
    }

    return { valid: true };
  }
}

// 创建单例实例
const textInApi = new TextInApiService();

export default textInApi;
