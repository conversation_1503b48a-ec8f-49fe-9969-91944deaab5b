import os
import json
import cv2
import easyocr
import numpy as np
from flask import Flask, request, jsonify, render_template
from PIL import Image
import io

# 初始化 Flask app 和 OCR Reader
app = Flask(__name__)
reader = easyocr.Reader(['ch_sim', 'en'])

@app.route('/')
def index():
    """渲染主页面"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_image():
    """处理图片上传和OCR识别"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if file:
        try:
            # 读取图片
            in_memory_file = io.BytesIO()
            file.save(in_memory_file)
            in_memory_file.seek(0)
            
            # 将图片数据转为OpenCV格式
            image_np_array = np.frombuffer(in_memory_file.read(), np.uint8)
            image = cv2.imdecode(image_np_array, cv2.IMREAD_COLOR)

            # OCR识别
            results = reader.readtext(image)
            
            # 格式化输出
            output = []
            for (bbox, text, prob) in results:
                # bbox是四个点的坐标 [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
                # 我们取左上角和右下角来定义一个简单的矩形框
                top_left = [int(coord) for coord in bbox[0]]
                bottom_right = [int(coord) for coord in bbox[2]]
                
                output.append({
                    'text': text,
                    'confidence': float(prob),
                    'bbox': [top_left[0], top_left[1], bottom_right[0], bottom_right[1]] # [x_min, y_min, x_max, y_max]
                })

            return jsonify(output)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5000) 