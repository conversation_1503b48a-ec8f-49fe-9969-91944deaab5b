import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Card, Image, Space, Typography, Alert } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import textInApi from '../services/textinApi';

const { Dragger } = Upload;
const { Title, Text } = Typography;

const ImageUpload = ({ onUploadSuccess, onUploadError, loading, setLoading }) => {
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);

  // 支持的文件类型
  const supportedTypes = [
    'image/png', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/tiff', 'image/webp',
    'application/pdf'
  ];

  const beforeUpload = useCallback((file) => {
    console.log('选择文件:', file);

    // 验证文件
    const validation = textInApi.validateFile(file);
    if (!validation.valid) {
      message.error(validation.error);
      return false;
    }

    // 检查是否已有文件
    if (fileList.length > 0) {
      message.warning('请先删除当前文件再上传新文件');
      return false;
    }

    // 创建预览URL
    const previewUrl = URL.createObjectURL(file);
    
    const newFile = {
      uid: file.uid,
      name: file.name,
      status: 'done',
      originFileObj: file,
      url: previewUrl,
      size: file.size,
      type: file.type
    };

    setFileList([newFile]);
    message.success('文件选择成功');
    
    return false; // 阻止自动上传
  }, [fileList]);

  const handleRemove = useCallback((file) => {
    console.log('删除文件:', file);
    
    // 释放预览URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
    
    setFileList([]);
    message.info('文件已删除');
  }, []);

  const handlePreview = useCallback((file) => {
    console.log('预览文件:', file);
    
    if (file.type.startsWith('image/')) {
      setPreviewImage(file.url);
      setPreviewVisible(true);
    } else {
      message.info('该文件类型不支持预览');
    }
  }, []);

  const handleRecognize = useCallback(async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件对象不存在');
      return;
    }

    setLoading(true);
    
    try {
      console.log('开始识别文件:', file.name);
      message.loading('正在识别中，请稍候...', 0);
      
      const result = await textInApi.recognizeFile(file);
      
      message.destroy();
      message.success('识别完成！');
      
      if (onUploadSuccess) {
        onUploadSuccess(result, file);
      }
    } catch (error) {
      console.error('识别失败:', error);
      message.destroy();
      message.error(`识别失败: ${error.message}`);
      
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [fileList, setLoading, onUploadSuccess, onUploadError]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: supportedTypes.join(','),
    fileList: fileList,
    beforeUpload: beforeUpload,
    onRemove: handleRemove,
    onPreview: handlePreview,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <Card title="文件上传" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Alert
            message="支持的文件格式"
            description="图片格式: PNG, JPG, JPEG, BMP, TIFF, WEBP | 文档格式: PDF"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Dragger {...uploadProps} style={{ padding: '20px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">
              点击或拖拽文件到此区域上传
            </p>
            <p className="ant-upload-hint">
              支持单个文件上传，文件大小不超过500MB
            </p>
          </Dragger>

          {fileList.length > 0 && (
            <Card size="small" title="已选择文件">
              <Space direction="vertical" style={{ width: '100%' }}>
                {fileList.map(file => (
                  <div key={file.uid} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px'
                  }}>
                    <Space>
                      <Text strong>{file.name}</Text>
                      <Text type="secondary">({formatFileSize(file.size)})</Text>
                    </Space>
                    <Space>
                      {file.type.startsWith('image/') && (
                        <Button 
                          icon={<EyeOutlined />} 
                          size="small" 
                          onClick={() => handlePreview(file)}
                        >
                          预览
                        </Button>
                      )}
                      <Button 
                        icon={<DeleteOutlined />} 
                        size="small" 
                        danger
                        onClick={() => handleRemove(file)}
                      >
                        删除
                      </Button>
                    </Space>
                  </div>
                ))}
              </Space>
            </Card>
          )}

          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="large"
            loading={loading}
            disabled={fileList.length === 0}
            onClick={handleRecognize}
            style={{ width: '100%' }}
          >
            {loading ? '识别中...' : '开始识别'}
          </Button>
        </Space>
      </Card>

      {/* 图片预览模态框 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => {
            setPreviewVisible(visible);
            if (!visible) {
              setPreviewImage('');
            }
          },
        }}
      />
    </div>
  );
};

export default ImageUpload;
